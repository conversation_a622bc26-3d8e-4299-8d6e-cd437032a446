'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { usePara<PERSON>, useRouter, useSearchParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { 
  Clock01Icon as ClockIcon, 
  Flag01Icon as FlagIcon, 
  Alert01Icon as AlertTriangleIcon, 
  CheckmarkCircle01Icon as CheckCircle2Icon,
  Cancel01Icon as XCircleIcon,
  Award01Icon as TrophyIcon,
  Rotate01Icon as RotateCcwIcon,
  ArrowLeft01Icon as ArrowLeftIcon,
  Home01Icon as HomeIcon
} from 'hugeicons-react';
import { Course, Quiz } from '@/types/lms';
import { useEnrollment } from '@/contexts/enrollment-context';
import { Question, QuestionBank } from '@/components/lms/final-exam';

const ExamPage: React.FC = () => {
  const params = useParams();
  const router = useRouter();
  const searchParams = useSearchParams();
  const courseId = params.courseId as string;
  const examType = searchParams.get('type') || 'final'; // 'final', 'chapter', 'module'
  const examId = searchParams.get('examId');

  const { getCourseById, updateSpecificCourseProgress } = useEnrollment();

  // Get the specific course data for this courseId
  const courseData = getCourseById(courseId);

  // If no course data, redirect to my-courses
  React.useEffect(() => {
    if (!courseData) {
      router.push('/my-courses');
    }
  }, [courseData, router]);

  // If no course data, show loading
  if (!courseData) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading exam...</p>
        </div>
      </div>
    );
  }

  const [currentQuestion, setCurrentQuestion] = useState(0);
  const [answers, setAnswers] = useState<{ [key: string]: any }>({});
  const [timeLeft, setTimeLeft] = useState<number | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showConfirmSubmit, setShowConfirmSubmit] = useState(false);
  const [flaggedQuestions, setFlaggedQuestions] = useState<Set<number>>(new Set());
  const [showResults, setShowResults] = useState(false);
  const [examResults, setExamResults] = useState<{
    score: number;
    correctAnswers: number;
    totalQuestions: number;
    results: { [key: string]: boolean };
  } | null>(null);
  const [examStarted, setExamStarted] = useState(false);
  const [showConfirmStart, setShowConfirmStart] = useState(true);
  const [timeWarnings, setTimeWarnings] = useState({
    fifteenMin: false,
    fiveMin: false,
    oneMin: false
  });
  const [showTimeWarning, setShowTimeWarning] = useState<{
    show: boolean;
    message: string;
    type: 'warning' | 'critical';
  }>({ show: false, message: '', type: 'warning' });

  // Get the current exam/quiz
  const getCurrentExam = (): Quiz | null => {
    if (examType === 'final') {
      return courseData.finalExam;
    }
    
    // Find chapter or module quiz by examId
    for (const courseModule of courseData.modules) {
      if (courseModule.moduleQuiz.id === examId) {
        return courseModule.moduleQuiz;
      }
      for (const chapter of courseModule.chapters) {
        if (chapter.quiz.id === examId) {
          return chapter.quiz;
        }
      }
    }
    return null;
  };

  const currentExam = getCurrentExam();

  // Initialize timer when exam starts
  useEffect(() => {
    if (examStarted && currentExam?.timeLimit && timeLeft === null) {
      setTimeLeft(currentExam.timeLimit * 60);
    }
  }, [examStarted, currentExam, timeLeft]);

  // Timer countdown with warnings
  useEffect(() => {
    if (examStarted && timeLeft !== null && timeLeft > 0 && !showResults) {
      const timer = setInterval(() => {
        setTimeLeft((prev) => {
          if (prev === null || prev <= 1) {
            handleTimeUp();
            return 0;
          }
          
          const newTime = prev - 1;
          
          // Time warnings with notifications
          if (newTime === 900 && !timeWarnings.fifteenMin) { // 15 minutes
            setTimeWarnings(prev => ({ ...prev, fifteenMin: true }));
            setShowTimeWarning({
              show: true,
              message: 'Peringatan: Sisa waktu 15 menit!',
              type: 'warning'
            });
            setTimeout(() => setShowTimeWarning(prev => ({ ...prev, show: false })), 5000);
          }
          if (newTime === 300 && !timeWarnings.fiveMin) { // 5 minutes
            setTimeWarnings(prev => ({ ...prev, fiveMin: true }));
            setShowTimeWarning({
              show: true,
              message: 'Peringatan: Sisa waktu 5 menit!',
              type: 'warning'
            });
            setTimeout(() => setShowTimeWarning(prev => ({ ...prev, show: false })), 5000);
          }
          if (newTime === 60 && !timeWarnings.oneMin) { // 1 minute
            setTimeWarnings(prev => ({ ...prev, oneMin: true }));
            setShowTimeWarning({
              show: true,
              message: 'PERINGATAN KRITIS: Sisa waktu 1 menit!',
              type: 'critical'
            });
            setTimeout(() => setShowTimeWarning(prev => ({ ...prev, show: false })), 8000);
          }
          
          return newTime;
        });
      }, 1000);
      return () => clearInterval(timer);
    }
  }, [examStarted, timeLeft, showResults, timeWarnings]);

  // Prevent navigation during exam
  useEffect(() => {
    if (examStarted && !showResults) {
      const handleBeforeUnload = (e: BeforeUnloadEvent) => {
        e.preventDefault();
        e.returnValue = '';
        return '';
      };

      window.addEventListener('beforeunload', handleBeforeUnload);
      return () => window.removeEventListener('beforeunload', handleBeforeUnload);
    }
  }, [examStarted, showResults]);

  const formatTime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const mins = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    
    if (hours > 0) {
      return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const getTimeStatus = (seconds: number) => {
    if (!currentExam?.timeLimit) return 'normal';
    const totalTime = currentExam.timeLimit * 60;
    const remaining = seconds / totalTime;
    
    if (remaining <= 0.1) return 'critical'; // Less than 10%
    if (remaining <= 0.25) return 'warning'; // Less than 25%
    return 'normal';
  };

  const getTimeColor = (status: string) => {
    switch (status) {
      case 'critical':
        return 'border-red-500 text-red-600 bg-red-50';
      case 'warning':
        return 'border-amber-500 text-amber-600 bg-amber-50';
      default:
        return 'border-blue-500 text-blue-600 bg-blue-50';
    }
  };

  const handleStartExam = () => {
    setExamStarted(true);
    setShowConfirmStart(false);
    if (currentExam?.timeLimit) {
      setTimeLeft(currentExam.timeLimit * 60);
    }
  };

  const handleAnswerChange = (questionId: string, answer: any) => {
    setAnswers((prev) => ({ ...prev, [questionId]: answer }));
  };

  const handleQuestionSelect = (questionIndex: number) => {
    setCurrentQuestion(questionIndex);
  };

  const handleToggleFlag = (questionIndex: number) => {
    setFlaggedQuestions(prev => {
      const newSet = new Set(prev);
      if (newSet.has(questionIndex)) {
        newSet.delete(questionIndex);
      } else {
        newSet.add(questionIndex);
      }
      return newSet;
    });
  };

  const getAnsweredQuestions = () => {
    const answered = new Set<number>();
    currentExam?.questions.forEach((question, index) => {
      if (answers[question.id] !== undefined && answers[question.id] !== '') {
        answered.add(index);
      }
    });
    return answered;
  };

  const handleTimeUp = () => {
    handleSubmitExam();
  };

  const handleSubmitConfirm = () => {
    setShowConfirmSubmit(true);
  };

  const handleSubmitExam = useCallback(() => {
    if (isSubmitting || !currentExam) return;
    setIsSubmitting(true);
    setShowConfirmSubmit(false);

    // Calculate score and results
    let correctAnswers = 0;
    const results: { [key: string]: boolean } = {};

    currentExam.questions.forEach((question) => {
      const userAnswer = answers[question.id];
      const isCorrect = userAnswer === question.correctAnswer;
      results[question.id] = isCorrect;
      if (isCorrect) {
        correctAnswers++;
      }
    });

    const score = Math.round((correctAnswers / currentExam.questions.length) * 100);
    
    // Store results in session storage for results page
    sessionStorage.setItem(`exam_answers_${examId || 'final'}`, JSON.stringify(answers));
    sessionStorage.setItem(`exam_results_${examId || 'final'}`, JSON.stringify(results));
    sessionStorage.setItem(`exam_flags_${examId || 'final'}`, JSON.stringify(Array.from(flaggedQuestions)));

    setExamResults({
      score,
      correctAnswers,
      totalQuestions: currentExam.questions.length,
      results
    });

    // Update course progress
    updateExamProgress(score);

    // Navigate to results page with score data
    const resultsUrl = `/my-courses/${courseId}/exam/results?type=${examType}&examId=${examId || 'final'}&score=${score}&correct=${correctAnswers}&total=${currentExam.questions.length}`;
    router.push(resultsUrl);
  }, [isSubmitting, currentExam, answers]);

  const updateExamProgress = (score: number) => {
    if (!currentExam) return;

    const newCourse = JSON.parse(JSON.stringify(courseData)) as Course;

    // Update the specific exam/quiz
    if (examType === 'final') {
      newCourse.finalExam.attempts += 1;
      newCourse.finalExam.lastScore = score;
      newCourse.finalExam.isPassed = score >= newCourse.finalExam.minimumScore;

      // Update certificate eligibility if passed
      if (newCourse.finalExam.isPassed) {
        const allModulesCompleted = newCourse.modules.every(
          (m) => m.chapters.every(
            (ch) => ch.contents.every((c) => c.isCompleted) && ch.quiz.isPassed
          ) && m.moduleQuiz.isPassed
        );
        
        if (allModulesCompleted) {
          newCourse.certificate.isEligible = true;
          newCourse.certificate.completionDate = new Date().toISOString().split('T')[0];
          newCourse.status = 'completed';
        }
      }
    } else {
      // Find and update chapter or module quiz
      for (const courseModule of newCourse.modules) {
        if (courseModule.moduleQuiz.id === examId) {
          courseModule.moduleQuiz.attempts += 1;
          courseModule.moduleQuiz.lastScore = score;
          courseModule.moduleQuiz.isPassed = score >= courseModule.moduleQuiz.minimumScore;
          break;
        }
        for (const chapter of courseModule.chapters) {
          if (chapter.quiz.id === examId) {
            chapter.quiz.attempts += 1;
            chapter.quiz.lastScore = score;
            chapter.quiz.isPassed = score >= chapter.quiz.minimumScore;
            break;
          }
        }
      }
    }

    updateSpecificCourseProgress(courseId, newCourse);
  };

  const handleRetakeExam = () => {
    // Reset all exam state
    setCurrentQuestion(0);
    setAnswers({});
    setFlaggedQuestions(new Set());
    setShowResults(false);
    setExamResults(null);
    setIsSubmitting(false);
    setShowConfirmSubmit(false);
    setExamStarted(true);
    if (currentExam?.timeLimit) {
      setTimeLeft(currentExam.timeLimit * 60);
    }
  };

  const handleBackToCourse = () => {
    router.push(`/my-courses/${courseId}`);
  };

  const handleBackHome = () => {
    router.push('/my-courses');
  };

  if (!currentExam) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardContent className="p-6 text-center">
            <XCircleIcon className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">Ujian Tidak Ditemukan</h3>
            <p className="text-gray-600 mb-4">Ujian yang diminta tidak dapat ditemukan.</p>
            <Button onClick={handleBackToCourse}>
              <ArrowLeftIcon className="mr-2 h-4 w-4" />
              Kembali ke Kursus
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  const answeredQuestions = getAnsweredQuestions();
  const progressPercentage = (answeredQuestions.size / currentExam.questions.length) * 100;
  const canSubmit = answeredQuestions.size > 0;

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b shadow-sm sticky top-0 z-10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <Button
                variant="outline"
                size="sm"
                onClick={handleBackToCourse}
                className="flex items-center space-x-2"
              >
                <ArrowLeftIcon className="h-4 w-4" />
                <span>Kembali</span>
              </Button>
              <TrophyIcon className="h-6 w-6 text-[var(--iai-primary)]" />
              <div>
                <h1 className="text-lg font-semibold text-gray-900">{currentExam.title}</h1>
                <p className="text-sm text-gray-600">{courseData.name}</p>
              </div>
            </div>
            
            {examStarted && timeLeft !== null && !showResults && (
              <div className="flex items-center gap-3">
                {/* Time Progress Bar */}
                {currentExam?.timeLimit && (
                  <div className="hidden sm:flex flex-col items-end min-w-[120px]">
                    <div className="text-xs text-gray-500 mb-1">
                      Sisa Waktu
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className={`h-2 rounded-full transition-all duration-1000 ${
                          getTimeStatus(timeLeft) === 'critical' ? 'bg-red-500' :
                          getTimeStatus(timeLeft) === 'warning' ? 'bg-amber-500' : 'bg-blue-500'
                        }`}
                        style={{ 
                          width: `${(timeLeft / (currentExam.timeLimit * 60)) * 100}%` 
                        }}
                      ></div>
                    </div>
                  </div>
                )}
                
                {/* Timer Display */}
                <div className={`
                  flex items-center gap-2 px-4 py-2 rounded-lg border-2 font-mono text-lg font-bold transition-all duration-300
                  ${getTimeColor(getTimeStatus(timeLeft))}
                  ${getTimeStatus(timeLeft) === 'critical' ? 'animate-pulse shadow-lg' : ''}
                `}>
                  <ClockIcon className="h-5 w-5" />
                  <div className="flex flex-col items-center">
                    <span className="leading-tight">{formatTime(timeLeft)}</span>
                    {currentExam?.timeLimit && (
                      <span className="text-xs opacity-75 leading-tight">
                        {getTimeStatus(timeLeft) === 'critical' ? 'SEGERA HABIS!' :
                         getTimeStatus(timeLeft) === 'warning' ? 'Perhatian' : 'Tersisa'}
                      </span>
                    )}
                  </div>
                </div>
              </div>
            )}

            {!examStarted || showResults ? (
              <div className="flex space-x-2">
                <Button variant="outline" onClick={handleBackToCourse}>
                  <ArrowLeftIcon className="mr-2 h-4 w-4" />
                  Kembali ke Kursus
                </Button>
                <Button variant="outline" onClick={handleBackHome}>
                  <HomeIcon className="mr-2 h-4 w-4" />
                  Dashboard
                </Button>
              </div>
            ) : null}
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Pre-exam Instructions */}
        {showConfirmStart && (
          <Card className="max-w-2xl mx-auto">
            <CardHeader>
              <CardTitle className="text-center">Instruksi Ujian</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                    <span className="text-sm font-medium text-blue-600">1</span>
                  </div>
                  <p className="text-gray-700">Pastikan koneksi internet Anda stabil</p>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                    <span className="text-sm font-medium text-blue-600">2</span>
                  </div>
                  <p className="text-gray-700">
                    Anda memiliki waktu {currentExam.timeLimit ? `${currentExam.timeLimit} menit` : 'tidak terbatas'} untuk menyelesaikan ujian
                  </p>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                    <span className="text-sm font-medium text-blue-600">3</span>
                  </div>
                  <p className="text-gray-700">Nilai minimum untuk lulus: {currentExam.minimumScore}%</p>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                    <span className="text-sm font-medium text-blue-600">4</span>
                  </div>
                  <p className="text-gray-700">Total soal: {currentExam.questions.length}</p>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                    <span className="text-sm font-medium text-blue-600">5</span>
                  </div>
                  <p className="text-gray-700">Maksimal percobaan: {currentExam.maxAttempts}</p>
                </div>
              </div>

              <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
                <div className="flex items-center space-x-2">
                  <AlertTriangleIcon className="h-5 w-5 text-amber-600" />
                  <p className="font-medium text-amber-800">Peringatan</p>
                </div>
                <p className="text-amber-700 text-sm mt-2">
                  Setelah ujian dimulai, jangan menutup browser atau meninggalkan halaman. 
                  Ujian akan otomatis diserahkan jika waktu habis.
                </p>
              </div>

              <div className="text-center">
                <Button 
                  size="lg" 
                  variant="iai"
                  onClick={handleStartExam}
                >
                  <TrophyIcon className="mr-2 h-5 w-5" />
                  Mulai Ujian
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Results Screen */}
        {showResults && examResults && (
          <div className="max-w-2xl mx-auto space-y-6">
            <Card className={`border-2 ${examResults.score >= currentExam.minimumScore ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}`}>
              <CardHeader className="text-center">
                <div className="flex justify-center mb-4">
                  {examResults.score >= currentExam.minimumScore ? (
                    <CheckCircle2Icon className="h-16 w-16 text-green-600" />
                  ) : (
                    <XCircleIcon className="h-16 w-16 text-red-600" />
                  )}
                </div>
                <CardTitle className="text-2xl">
                  {examResults.score >= currentExam.minimumScore ? 'Selamat! Anda Lulus' : 'Maaf, Anda Belum Lulus'}
                </CardTitle>
              </CardHeader>
              <CardContent className="text-center space-y-4">
                <div className="text-4xl font-bold text-gray-900">
                  {examResults.score}%
                </div>
                <div className="text-gray-600">
                  {examResults.correctAnswers} dari {examResults.totalQuestions} soal dijawab benar
                </div>
                <div className="text-sm text-gray-500">
                  Nilai minimum untuk lulus: {currentExam.minimumScore}%
                </div>
                
                <div className="flex justify-center space-x-4 mt-6">
                  {examResults.score < currentExam.minimumScore && currentExam.attempts < currentExam.maxAttempts && (
                    <Button onClick={handleRetakeExam}>
                      <RotateCcwIcon className="mr-2 h-4 w-4" />
                      Ulangi Ujian
                    </Button>
                  )}
                  
                  <Button variant="outline" onClick={handleBackToCourse}>
                    <ArrowLeftIcon className="mr-2 h-4 w-4" />
                    Kembali ke Kursus
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Exam Interface */}
        {examStarted && !showResults && (
          <div className="grid grid-cols-1 xl:grid-cols-4 gap-8">
            {/* Question Area */}
            <div className="xl:col-span-3 space-y-6">
              {/* Progress */}
              <div className="space-y-2">
                <div className="flex justify-between text-sm text-gray-600">
                  <span>Soal {currentQuestion + 1} dari {currentExam.questions.length}</span>
                  <span>{Math.round(progressPercentage)}% Selesai</span>
                </div>
                <Progress value={progressPercentage} className="h-3" />
              </div>

              {/* Current Question */}
              <Question
                question={currentExam.questions[currentQuestion]}
                questionNumber={currentQuestion + 1}
                totalQuestions={currentExam.questions.length}
                selectedAnswer={answers[currentExam.questions[currentQuestion].id]}
                onAnswerChange={handleAnswerChange}
                disabled={isSubmitting}
              />

              {/* Navigation */}
              <div className="flex justify-between items-center">
                <Button
                  variant="outline"
                  onClick={() => setCurrentQuestion(prev => Math.max(0, prev - 1))}
                  disabled={currentQuestion === 0 || isSubmitting}
                >
                  Previous
                </Button>

                <div className="flex items-center space-x-3">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleToggleFlag(currentQuestion)}
                    disabled={isSubmitting}
                    className={flaggedQuestions.has(currentQuestion) ? 'bg-yellow-100 border-yellow-400' : ''}
                  >
                    <FlagIcon className={`h-4 w-4 mr-2 ${flaggedQuestions.has(currentQuestion) ? 'text-yellow-600' : ''}`} />
                    Flag
                  </Button>
                </div>

                <Button
                  variant="iai"
                  onClick={() => setCurrentQuestion(prev => 
                    Math.min(currentExam.questions.length - 1, prev + 1)
                  )}
                  disabled={currentQuestion === currentExam.questions.length - 1 || isSubmitting}
                >
                  Next
                </Button>
              </div>
            </div>

            {/* Question Bank Sidebar */}
            <div className="xl:col-span-1">
              <QuestionBank
                questions={currentExam.questions}
                currentQuestion={currentQuestion}
                answeredQuestions={answeredQuestions}
                onQuestionSelect={handleQuestionSelect}
                flaggedQuestions={flaggedQuestions}
                onToggleFlag={handleToggleFlag}
                onSubmit={handleSubmitConfirm}
                canSubmit={canSubmit}
                isSubmitting={isSubmitting}
              />
            </div>
          </div>
        )}
      </div>

      {/* Submit Confirmation Dialog - Popup with Blurred Background */}
      {showConfirmSubmit && (
        <div className="fixed inset-0 backdrop-blur-sm flex items-center justify-center z-50">
          <Card className="w-full max-w-md mx-4 shadow-2xl border-2">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2 text-amber-700">
                <AlertTriangleIcon className="h-5 w-5" />
                <span>Konfirmasi Penyerahan</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-gray-600">
                Apakah Anda yakin ingin menyerahkan ujian? Pastikan semua jawaban sudah benar.
              </p>
              <div className="grid grid-cols-2 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">{answeredQuestions.size}</div>
                  <div className="text-xs text-gray-500">Terjawab</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-gray-400">
                    {currentExam.questions.length - answeredQuestions.size}
                  </div>
                  <div className="text-xs text-gray-500">Belum</div>
                </div>
              </div>
              <div className="flex space-x-2">
                <Button
                  variant="outline"
                  className="flex-1"
                  onClick={() => setShowConfirmSubmit(false)}
                >
                  Batal
                </Button>
                <Button
                  variant="iai"
                  className="flex-1"
                  onClick={handleSubmitExam}
                  disabled={isSubmitting}
                >
                  {isSubmitting ? 'Menyerahkan...' : 'Ya, Serahkan'}
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Time Warning Toast */}
      {showTimeWarning.show && (
        <div className="fixed top-4 right-4 z-50 animate-in slide-in-from-top-2">
          <div
            className={`flex items-center space-x-3 rounded-lg px-6 py-4 shadow-lg border-2 ${
              showTimeWarning.type === 'critical'
                ? 'bg-red-50 text-red-800 border-red-200'
                : 'bg-amber-50 text-amber-800 border-amber-200'
            } min-w-[320px]`}
          >
            <div className={`flex-shrink-0 ${
              showTimeWarning.type === 'critical' ? 'animate-pulse' : ''
            }`}>
              <ClockIcon className={`h-6 w-6 ${
                showTimeWarning.type === 'critical' ? 'text-red-600' : 'text-amber-600'
              }`} />
            </div>
            <div>
              <p className="font-semibold text-sm">{showTimeWarning.message}</p>
              <p className="text-xs opacity-75 mt-1">
                {showTimeWarning.type === 'critical' 
                  ? 'Segera serahkan ujian Anda!'
                  : 'Pastikan untuk menyerahkan ujian tepat waktu.'
                }
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ExamPage;