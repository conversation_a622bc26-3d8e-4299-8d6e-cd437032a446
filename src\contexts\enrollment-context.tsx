'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { architectureCourse } from '@/constants/shared-course-data';
import { Course } from '@/types/lms';

interface EnrollmentData {
  isEnrolled: boolean;
  courseData: Course;
  enrollmentTimestamp: number;
  expirationTime: number; // 10 minutes in milliseconds
}

interface MultipleEnrollmentData {
  enrolledCourses: Course[];
  enrollmentTimestamp: number;
  expirationTime: number; // 10 minutes in milliseconds
}

interface EnrollmentContextType {
  isEnrolled: boolean;
  courseData: Course;
  enrollInCourse: () => void;
  enrollInCourseWithPurchase: (course: Course) => void;
  updateCourseProgress: (updatedCourse: Course) => void;
  enrolledCourses: Course[];
  isEnrolledInCourse: (courseId: string) => boolean;
  getCourseById: (courseId: string) => Course | undefined;
  updateSpecificCourseProgress: (courseId: string, updatedCourse: Course) => void;
}

const EnrollmentContext = createContext<EnrollmentContextType | undefined>(
  undefined
);

export const useEnrollment = () => {
  const context = useContext(EnrollmentContext);
  if (!context) {
    throw new Error('useEnrollment must be used within an EnrollmentProvider');
  }
  return context;
};

interface EnrollmentProviderProps {
  children: ReactNode;
}

export const EnrollmentProvider: React.FC<EnrollmentProviderProps> = ({
  children
}) => {
  const [isEnrolled, setIsEnrolled] = useState(false);
  const [courseData, setCourseData] = useState<Course>(architectureCourse);
  const [enrolledCourses, setEnrolledCourses] = useState<Course[]>([]);

  const STORAGE_KEY = 'lms-enrollment-data';
  const MULTIPLE_STORAGE_KEY = 'lms-multiple-enrollment-data';
  const EXPIRATION_TIME = 10 * 60 * 1000; // 10 minutes in milliseconds

  // Load persisted data on component mount
  useEffect(() => {
    const loadPersistedData = () => {
      try {
        // Try to load multiple enrollment data first
        const multipleStored = localStorage.getItem(MULTIPLE_STORAGE_KEY);
        if (multipleStored) {
          const multipleData: MultipleEnrollmentData = JSON.parse(multipleStored);
          const now = Date.now();

          // Check if enrollment has expired
          if (now < multipleData.expirationTime) {
            setEnrolledCourses(multipleData.enrolledCourses);
            setIsEnrolled(multipleData.enrolledCourses.length > 0);
            if (multipleData.enrolledCourses.length > 0) {
              setCourseData(multipleData.enrolledCourses[0]); // Set first course as primary
            }
          } else {
            // Clear expired data
            localStorage.removeItem(MULTIPLE_STORAGE_KEY);
          }
          return;
        }

        // Fallback to old single enrollment data for backward compatibility
        const stored = localStorage.getItem(STORAGE_KEY);
        if (stored) {
          const enrollmentData: EnrollmentData = JSON.parse(stored);
          const now = Date.now();

          // Check if enrollment has expired
          if (now < enrollmentData.expirationTime) {
            setIsEnrolled(enrollmentData.isEnrolled);
            setCourseData(enrollmentData.courseData);
            setEnrolledCourses([enrollmentData.courseData]);

            // Migrate to new format
            const multipleData: MultipleEnrollmentData = {
              enrolledCourses: [enrollmentData.courseData],
              enrollmentTimestamp: enrollmentData.enrollmentTimestamp,
              expirationTime: enrollmentData.expirationTime
            };
            localStorage.setItem(MULTIPLE_STORAGE_KEY, JSON.stringify(multipleData));
            localStorage.removeItem(STORAGE_KEY); // Remove old format
          } else {
            // Clear expired data
            localStorage.removeItem(STORAGE_KEY);
          }
        }
      } catch (error) {
        console.error('Failed to load enrollment data:', error);
        localStorage.removeItem(STORAGE_KEY);
        localStorage.removeItem(MULTIPLE_STORAGE_KEY);
      }
    };

    loadPersistedData();
  }, []);

  // Persist enrollment data to localStorage
  const persistEnrollmentData = (course: Course) => {
    const now = Date.now();

    try {
      // Update enrolled courses state
      setEnrolledCourses(prev => {
        const isAlreadyEnrolled = prev.some(c => c.id === course.id);
        let updatedCourses;

        if (isAlreadyEnrolled) {
          // Update existing course
          updatedCourses = prev.map(c => c.id === course.id ? course : c);
        } else {
          // Add new course
          updatedCourses = [...prev, course];
        }

        // Save to localStorage with new format
        const multipleData: MultipleEnrollmentData = {
          enrolledCourses: updatedCourses,
          enrollmentTimestamp: now,
          expirationTime: now + EXPIRATION_TIME
        };
        localStorage.setItem(MULTIPLE_STORAGE_KEY, JSON.stringify(multipleData));

        return updatedCourses;
      });

      // Set up automatic cleanup after expiration
      setTimeout(() => {
        localStorage.removeItem(MULTIPLE_STORAGE_KEY);
        setIsEnrolled(false);
        setEnrolledCourses([]);
        setCourseData(architectureCourse);
      }, EXPIRATION_TIME);
    } catch (error) {
      console.error('Failed to persist enrollment data:', error);
    }
  };

  const enrollInCourse = () => {
    setIsEnrolled(true);
    const updatedCourse = {
      ...architectureCourse,
      status: 'in-progress' as const
    };
    setCourseData(updatedCourse);
    persistEnrollmentData(updatedCourse);
  };

  const enrollInCourseWithPurchase = (course: Course) => {
    setIsEnrolled(true);
    const updatedCourse = {
      ...course,
      status: 'in-progress' as const,
      totalProgress: 0
    };
    setCourseData(updatedCourse);
    persistEnrollmentData(updatedCourse);
  };

  const updateCourseProgress = (updatedCourse: Course) => {
    setCourseData(updatedCourse);
    // Update persisted data with new progress
    if (isEnrolled) {
      persistEnrollmentData(updatedCourse);
    }
  };

  // Check if user is enrolled in a specific course
  const isEnrolledInCourse = (courseId: string): boolean => {
    return enrolledCourses.some(course => course.id === courseId);
  };

  // Get course data by courseId
  const getCourseById = (courseId: string): Course | undefined => {
    return enrolledCourses.find(course => course.id === courseId);
  };

  // Update progress for a specific course
  const updateSpecificCourseProgress = (courseId: string, updatedCourse: Course) => {
    // Update the specific course in enrolledCourses array
    setEnrolledCourses(prev => {
      const updatedCourses = prev.map(course =>
        course.id === courseId ? updatedCourse : course
      );

      // Update localStorage with new data
      const now = Date.now();
      const multipleData: MultipleEnrollmentData = {
        enrolledCourses: updatedCourses,
        enrollmentTimestamp: now,
        expirationTime: now + EXPIRATION_TIME
      };
      localStorage.setItem(MULTIPLE_STORAGE_KEY, JSON.stringify(multipleData));

      return updatedCourses;
    });

    // If this is the current courseData, update it too
    if (courseData.id === courseId) {
      setCourseData(updatedCourse);
    }
  };

  const value = {
    isEnrolled,
    courseData,
    enrollInCourse,
    enrollInCourseWithPurchase,
    updateCourseProgress,
    enrolledCourses,
    isEnrolledInCourse,
    getCourseById,
    updateSpecificCourseProgress
  };

  return (
    <EnrollmentContext.Provider value={value}>
      {children}
    </EnrollmentContext.Provider>
  );
};