'use client';

import React, { useState, useCallback } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle
} from '@/components/ui/dialog';
import { Ta<PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import {
  BookOpen01Icon as BookOpenIcon,
  ChartIcon as BarChartIcon,
  Award01Icon as TrophyIcon,
  Award01Icon as AwardIcon,
  Calendar01Icon as CalendarIcon,
  Download01Icon as DownloadIcon,
  Building02Icon as BuildingIcon,
  ArrowLeft01Icon as ArrowLeftIcon
} from 'hugeicons-react';
import Link from 'next/link';
import { useParams, useRouter } from 'next/navigation';

import { Course, Quiz } from '@/types/lms';
import { useEnrollment } from '@/contexts/enrollment-context';
import {
  QuizModal,
  CertificateTemplate,
  CourseTab,
  ProgressTab,
  ExamTab,
  CertificateTab
} from '@/components/lms';

const CoursePage: React.FC = () => {
  const params = useParams();
  const router = useRouter();
  const courseId = params.courseId as string;
  const { getCourseById, updateSpecificCourseProgress, enrolledCourses } = useEnrollment();

  // Get the specific course data for this courseId
  const courseData = getCourseById(courseId);

  // If course not found, redirect to my-courses
  React.useEffect(() => {
    if (!courseData && enrolledCourses.length > 0) {
      router.push('/my-courses');
    }
  }, [courseData, enrolledCourses, router]);

  // If no course data, show loading or redirect
  if (!courseData) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading course...</p>
        </div>
      </div>
    );
  }


  const [expandedContents, setExpandedContents] = useState<{
    [key: string]: boolean;
  }>({});
  const [expandedModules, setExpandedModules] = useState<{
    [key: string]: boolean;
  }>({});
  const [expandedChapters, setExpandedChapters] = useState<{
    [key: string]: boolean;
  }>({});
  const [currentQuiz, setCurrentQuiz] = useState<Quiz | null>(null);
  const [showCertificate, setShowCertificate] = useState(false);
  const [activeTab, setActiveTab] = useState('course');

  const toggleContent = useCallback((contentId: string) => {
    setExpandedContents((prev) => ({ ...prev, [contentId]: !prev[contentId] }));
  }, []);

  const toggleModule = useCallback((moduleId: string) => {
    setExpandedModules((prev) => ({ ...prev, [moduleId]: !prev[moduleId] }));
  }, []);

  const toggleChapter = useCallback((chapterId: string) => {
    setExpandedChapters((prev) => ({ ...prev, [chapterId]: !prev[chapterId] }));
  }, []);

  const expandAllModules = useCallback(() => {
    const newExpandedModules: { [key: string]: boolean } = {};
    courseData.modules.forEach((module) => {
      if (module.isUnlocked) {
        newExpandedModules[module.id] = true;
      }
    });
    setExpandedModules(newExpandedModules);
  }, [courseData.modules]);

  const collapseAllModules = useCallback(() => {
    setExpandedModules({});
  }, []);

  const expandAllChaptersInModule = useCallback(
    (moduleId: string) => {
      const courseModule = courseData.modules.find((m) => m.id === moduleId);
      if (!courseModule) return;

      const newExpandedChapters = { ...expandedChapters };
      courseModule.chapters.forEach((chapter) => {
        if (chapter.isUnlocked) {
          newExpandedChapters[chapter.id] = true;
        }
      });
      setExpandedChapters(newExpandedChapters);
    },
    [courseData.modules, expandedChapters]
  );

  const collapseAllChaptersInModule = useCallback(
    (moduleId: string) => {
      const courseModule = courseData.modules.find((m) => m.id === moduleId);
      if (!courseModule) return;

      setExpandedModules((prev) => ({ ...prev, [moduleId]: false }));

      const newExpandedContents = { ...expandedContents };
      const newExpandedChapters = { ...expandedChapters };
      courseModule.chapters.forEach((chapter) => {
        delete newExpandedChapters[chapter.id];
      });
      setExpandedChapters(newExpandedChapters);
    },
    [courseData.modules, expandedChapters]
  );

  const toggleContentComplete = useCallback(
    (contentId: string) => {
      const newCourse = JSON.parse(JSON.stringify(courseData)) as Course;

      let contentFound = false;
      for (const courseModule of newCourse.modules) {
        for (const chapter of courseModule.chapters) {
          const content = chapter.contents.find((c) => c.id === contentId);
          if (content) {
            content.isCompleted = !content.isCompleted;
            contentFound = true;

            const completedContents = chapter.contents.filter(
              (c) => c.isCompleted
            ).length;
            const nextChapterIndex = chapter.order;
            const nextChapter = courseModule.chapters.find(
              (ch) => ch.order === nextChapterIndex + 1
            );

            if (
              nextChapter &&
              completedContents === chapter.contents.length &&
              chapter.quiz.isPassed
            ) {
              nextChapter.isUnlocked = true;
            }

            const allChaptersCompleted = courseModule.chapters.every(
              (ch) =>
                ch.contents.every((c) => c.isCompleted) && ch.quiz.isPassed
            );

            if (allChaptersCompleted && courseModule.moduleQuiz.isPassed) {
              const nextModuleIndex = courseModule.order;
              const nextModule = newCourse.modules.find(
                (m) => m.order === nextModuleIndex + 1
              );
              if (nextModule) {
                nextModule.isUnlocked = true;
                if (nextModule.chapters.length > 0) {
                  nextModule.chapters[0].isUnlocked = true;
                }
              }
            }

            break;
          }
        }
        if (contentFound) break;
      }

      updateSpecificCourseProgress(courseId, newCourse);
    },
    [courseData, courseId, updateSpecificCourseProgress]
  );

  const startQuiz = useCallback(
    (quizId: string) => {
      let quiz: Quiz | undefined;

      // Check if it's the final exam
      if (courseData.finalExam.id === quizId) {
        // Navigate to dedicated exam page for final exam
        router.push(`/my-courses/${courseId}/exam?type=final&examId=${quizId}`);
        return;
      }

      // Check module and chapter quizzes
      for (const courseModule of courseData.modules) {
        for (const chapter of courseModule.chapters) {
          if (chapter.quiz.id === quizId) {
            quiz = chapter.quiz;
            // For now, chapter quizzes can also use the exam page or modal
            // You can decide whether to navigate or use modal
            setCurrentQuiz({ ...quiz });
            return;
          }
        }
        if (courseModule.moduleQuiz.id === quizId) {
          quiz = courseModule.moduleQuiz;
          // For now, module quizzes can also use the exam page or modal
          // You can decide whether to navigate or use modal
          setCurrentQuiz({ ...quiz });
          return;
        }
      }
    },
    [courseData, courseId, router]
  );

  const handleQuizComplete = useCallback(
    (score: number) => {
      if (!currentQuiz) return;

      const newCourse = JSON.parse(JSON.stringify(courseData)) as Course;

      const updateQuiz = (quiz: Quiz) => {
        quiz.attempts += 1;
        quiz.lastScore = score;
        quiz.isPassed = score >= quiz.minimumScore;
      };

      for (const courseModule of newCourse.modules) {
        for (const chapter of courseModule.chapters) {
          if (chapter.quiz.id === currentQuiz.id) {
            updateQuiz(chapter.quiz);

            const allContentsCompleted = chapter.contents.every(
              (c) => c.isCompleted
            );
            // Unlock next chapter if: (quiz passed OR max attempts reached) AND all contents completed
            if ((chapter.quiz.isPassed || chapter.quiz.attempts >= chapter.quiz.maxAttempts) && allContentsCompleted) {
              const nextChapter = courseModule.chapters.find(
                (ch) => ch.order === chapter.order + 1
              );
              if (nextChapter) {
                nextChapter.isUnlocked = true;
              }
            }
            break;
          }
        }

        if (courseModule.moduleQuiz.id === currentQuiz.id) {
          updateQuiz(courseModule.moduleQuiz);

          const allChaptersCompleted = courseModule.chapters.every(
            (ch) => ch.contents.every((c) => c.isCompleted) && (ch.quiz.isPassed || ch.quiz.attempts >= ch.quiz.maxAttempts)
          );
          if ((courseModule.moduleQuiz.isPassed || courseModule.moduleQuiz.attempts >= courseModule.moduleQuiz.maxAttempts) && allChaptersCompleted) {
            const nextModule = newCourse.modules.find(
              (m) => m.order === courseModule.order + 1
            );
            if (nextModule) {
              nextModule.isUnlocked = true;
              if (nextModule.chapters.length > 0) {
                nextModule.chapters[0].isUnlocked = true;
              }
            }
          }
        }
      }

      if (newCourse.finalExam.id === currentQuiz.id) {
        updateQuiz(newCourse.finalExam);

        const allModulesCompleted = newCourse.modules.every(
          (m) =>
            m.chapters.every(
              (ch) =>
                ch.contents.every((c) => c.isCompleted) && (ch.quiz.isPassed || ch.quiz.attempts >= ch.quiz.maxAttempts)
            ) && (m.moduleQuiz.isPassed || m.moduleQuiz.attempts >= m.moduleQuiz.maxAttempts)
        );

        if (newCourse.finalExam.isPassed && allModulesCompleted) {
          newCourse.certificate.isEligible = true;
          newCourse.certificate.completionDate = new Date()
            .toISOString()
            .split('T')[0];
          newCourse.status = 'completed';
        }
      }

      updateSpecificCourseProgress(courseId, newCourse);
      setCurrentQuiz(null);
    },
    [currentQuiz, courseData, courseId, updateSpecificCourseProgress]
  );

  const generateCertificate = useCallback(() => {
    if (courseData.certificate.isEligible) {
      const updatedCourse = {
        ...courseData,
        certificate: {
          ...courseData.certificate,
          isGenerated: true,
          certificateUrl: `#certificate-${courseData.id}`
        }
      };
      updateSpecificCourseProgress(courseId, updatedCourse);
      setShowCertificate(true);
    }
  }, [courseData, courseId, updateSpecificCourseProgress]);

  const handleNavigateToSection = useCallback(
    (moduleId: string, chapterId?: string) => {
      // Set active tab first
      setActiveTab('course');
      
      // Expand the module and chapter
      setExpandedModules((prev) => ({ ...prev, [moduleId]: true }));
      if (chapterId) {
        setExpandedChapters((prev) => ({ ...prev, [chapterId]: true }));
      }
      
      // Smooth scroll to the target element after a short delay to allow DOM updates
      setTimeout(() => {
        const targetId = chapterId ? `chapter-${chapterId}` : `module-${moduleId}`;
        const targetElement = document.getElementById(targetId);
        
        if (targetElement) {
          targetElement.scrollIntoView({
            behavior: 'smooth',
            block: 'start',
            inline: 'nearest'
          });
          
          // Optional: Add a brief highlight effect
          targetElement.classList.add('ring-2', 'ring-blue-400', 'ring-opacity-75');
          setTimeout(() => {
            targetElement.classList.remove('ring-2', 'ring-blue-400', 'ring-opacity-75');
          }, 2000);
        }
      }, 100);
    },
    []
  );

  const handleDownloadPDF = useCallback(() => {
    // Create a new window for printing the certificate
    const printWindow = window.open('', '_blank');
    if (!printWindow) return;

    // Get the certificate content
    const certificateElement = document.querySelector('[data-certificate-template]');
    if (!certificateElement) {
      // Fallback: just print the current page
      window.print();
      return;
    }

    // Create the print document
    printWindow.document.write(`
      <!DOCTYPE html>
      <html>
        <head>
          <title>Sertifikat - ${courseData.name}</title>
          <style>
            @media print {
              @page {
                size: A4 landscape;
                margin: 0;
              }
              body {
                margin: 0;
                padding: 20px;
                font-family: Arial, sans-serif;
              }
            }
            body {
              margin: 0;
              padding: 20px;
              font-family: Arial, sans-serif;
            }
          </style>
        </head>
        <body>
          ${certificateElement.outerHTML}
        </body>
      </html>
    `);
    
    printWindow.document.close();
    
    // Wait for content to load then print
    setTimeout(() => {
      printWindow.focus();
      printWindow.print();
      printWindow.close();
    }, 250);
  }, [courseData.name]);

  const completedChapters = courseData.modules.reduce(
    (total, module) =>
      total +
      module.chapters.filter(
        (ch) => ch.contents.every((c) => c.isCompleted) && ch.quiz.isPassed
      ).length,
    0
  );

  const totalChapters = courseData.modules.reduce(
    (total, module) => total + module.chapters.length,
    0
  );
  const overallProgress =
    totalChapters > 0 ? (completedChapters / totalChapters) * 100 : 0;

  return (
    <div className='min-h-screen bg-gray-50 overflow-auto'>
      <div className='mx-auto max-w-full space-y-6 p-8'>
        {/* Header with Back Button */}
        <div className='flex items-center justify-between'>
          <div className='flex items-center space-x-4'>
            <Link href='/my-courses'>
              <Button
                variant='outline'
                size='sm'
                className='flex items-center space-x-2'
              >
                <ArrowLeftIcon className='h-4 w-4' />
                <span>Kembali ke Kursus Saya</span>
              </Button>
            </Link>
            <div className='flex items-center space-x-3'>
              <BuildingIcon className='h-8 w-8 text-[var(--iai-primary)]' />
              <div>
                <h1 className='text-3xl font-bold text-gray-900'>
                  {courseData.name}
                </h1>
                <p className='text-gray-600'>Kode Kursus: {courseData.code}</p>
                <p className='text-gray-600'>
                  Instruktur: {courseData.instructor}
                </p>
                <div className='mt-2 flex items-center space-x-4'>
                  <span className='text-sm text-gray-500'>
                    <CalendarIcon className='mr-1 inline h-4 w-4' />
                    {courseData.startDate} - {courseData.endDate}
                  </span>
                  <Badge
                    variant={
                      courseData.status === 'completed'
                        ? 'default'
                        : 'secondary'
                    }
                  >
                    {courseData.status === 'completed'
                      ? 'Selesai'
                      : 'Sedang Belajar'}
                  </Badge>
                </div>
              </div>
            </div>
          </div>
          <div className='flex items-center space-x-4'>
            <div className='text-right'>
              <p className='text-sm text-gray-500'>Kemajuan Keseluruhan</p>
              <div className='flex items-center space-x-2'>
                <Progress value={overallProgress} className='w-32' />
                <span className='text-sm font-medium'>
                  {Math.round(overallProgress)}%
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className='mb-6'>
          <TabsList className='grid w-full grid-cols-4'>
            <TabsTrigger value='course' className='flex items-center space-x-2'>
              <BookOpenIcon className='h-4 w-4' />
              <span>Konten Kursus</span>
            </TabsTrigger>
            <TabsTrigger
              value='progress'
              className='flex items-center space-x-2'
            >
              <BarChartIcon className='h-4 w-4' />
              <span>Kemajuan</span>
            </TabsTrigger>
            <TabsTrigger value='exam' className='flex items-center space-x-2'>
              <TrophyIcon className='h-4 w-4' />
              <span>Final Exam</span>
            </TabsTrigger>
            <TabsTrigger
              value='certificate'
              className='flex items-center space-x-2'
            >
              <AwardIcon className='h-4 w-4' />
              <span>Sertifikat</span>
            </TabsTrigger>
          </TabsList>

          <TabsContent value='course' className='mt-4'>
            <CourseTab
              courseData={courseData}
              expandedModules={expandedModules}
              expandedChapters={expandedChapters}
              expandedContents={expandedContents}
              onToggleModule={toggleModule}
              onToggleChapter={toggleChapter}
              onToggleContent={toggleContent}
              onToggleContentComplete={toggleContentComplete}
              onStartQuiz={startQuiz}
              onNavigateToSection={handleNavigateToSection}
              onExpandAllModules={expandAllModules}
              onCollapseAllModules={collapseAllModules}
              onExpandAllChaptersInModule={expandAllChaptersInModule}
              onCollapseAllChaptersInModule={collapseAllChaptersInModule}
            />
          </TabsContent>

          <TabsContent value='progress' className='mt-4'>
            <ProgressTab
              courseData={courseData}
              overallProgress={overallProgress}
            />
          </TabsContent>

          <TabsContent value='exam' className='mt-4'>
            <ExamTab courseData={courseData} onStartQuiz={startQuiz} />
          </TabsContent>

          <TabsContent value='certificate' className='mt-4'>
            <CertificateTab
              courseData={courseData}
              institution={{
                id: 'iai-indonesia',
                name: 'Indonesian Institute of Architects',
                shortName: 'IAI',
                website: 'https://iai.or.id',
                certificateTemplate: {
                  primaryColor: '#1e40af',
                  secondaryColor: '#f59e0b',
                  signatoryName: 'Ar. Georgius Budi Yulianto, IAI, AA',
                  signatoryTitle: 'Ketua Umum IAI 2024-2027'
                }
              }}
              overallProgress={overallProgress}
              onGenerateCertificate={generateCertificate}
              onShowCertificate={() => setShowCertificate(true)}
              onDownloadPDF={handleDownloadPDF}
            />
          </TabsContent>
        </Tabs>

        {/* Quiz Modal - Only for chapter and module quizzes */}
        {currentQuiz && (
          <QuizModal
            quiz={currentQuiz}
            isOpen={true}
            onComplete={handleQuizComplete}
            onClose={() => setCurrentQuiz(null)}
          />
        )}

        {/* Certificate Modal */}
        <Dialog open={showCertificate} onOpenChange={setShowCertificate}>
          <DialogContent className='max-h-[90vh] max-w-6xl p-0'>
            <div className='flex h-full max-h-[90vh] flex-col'>
              <DialogHeader className='flex-shrink-0 border-b px-6 py-4'>
                <DialogTitle className='flex items-center space-x-2'>
                  <AwardIcon className='h-5 w-5' />
                  <span>Sertifikat Anda</span>
                </DialogTitle>
              </DialogHeader>
              <div className='flex-1 overflow-y-auto px-6 py-6'>
                <CertificateTemplate
                  course={courseData}
                  institution={{
                    id: 'iai-indonesia',
                    name: 'Indonesian Institute of Architects',
                    shortName: 'IAI',
                    website: 'https://iai.or.id',
                    certificateTemplate: {
                      primaryColor: '#1e40af',
                      secondaryColor: '#f59e0b',
                      signatoryName: 'Ar. Georgius Budi Yulianto, IAI, AA',
                      signatoryTitle:
                        'Ketua Umum IAI 2024-2027'
                    }
                  }}
                  studentName='John Doe'
                  completionDate={
                    courseData.certificate.completionDate ||
                    new Date().toISOString().split('T')[0]
                  }
                />
              </div>
              <div className='flex-shrink-0 border-t bg-white px-6 py-4'>
                <div className='flex justify-end space-x-2'>
                  <Button
                    variant='outline'
                    onClick={() => setShowCertificate(false)}
                  >
                    Tutup
                  </Button>
                  <Button onClick={handleDownloadPDF}>
                    <DownloadIcon className='mr-2 h-4 w-4' />
                    Unduh PDF
                  </Button>
                </div>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>
    </div>
  );
};

export default CoursePage;