import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { BookMarked, Target, Award, Maximize, Minimize, X } from 'lucide-react';
import { Course } from '@/types/lms';
import { TableOfContents } from '../table-of-contents';
import { ModuleSection } from '../module-section';

interface CourseTabProps {
  courseData: Course;
  expandedModules: { [key: string]: boolean };
  expandedChapters: { [key: string]: boolean };
  expandedContents: { [key: string]: boolean };
  onToggleModule: (moduleId: string) => void;
  onToggleChapter: (chapterId: string) => void;
  onToggleContent: (contentId: string) => void;
  onToggleContentComplete: (contentId: string) => void;
  onStartQuiz: (quizId: string) => void;
  onNavigateToSection: (moduleId: string, chapterId?: string) => void;
  onExpandAllModules: () => void;
  onCollapseAllModules: () => void;
  onExpandAllChaptersInModule: (moduleId: string) => void;
  onCollapseAllChaptersInModule: (moduleId: string) => void;
}

export const CourseTab: React.FC<CourseTabProps> = ({
  courseData,
  expandedModules,
  expandedChapters,
  expandedContents,
  onToggleModule,
  onToggleChapter,
  onToggleContent,
  onToggleContentComplete,
  onStartQuiz,
  onNavigateToSection,
  onExpandAllModules,
  onCollapseAllModules,
  onExpandAllChaptersInModule,
  onCollapseAllChaptersInModule
}) => {
  const [showCourseStructure, setShowCourseStructure] = useState(true);

  const allModulesExpanded = courseData.modules
    .filter((m) => m.isUnlocked)
    .every((m) => expandedModules[m.id]);

  return (
    <div className="h-[calc(100vh-210px)] overflow-hidden">
      <div className='grid grid-cols-1 gap-6 lg:grid-cols-4 min-w-0 h-full'>
        {/* Sidebar */}
        <div className='lg:col-span-1 min-w-0 overflow-y-auto'>
          <div className='w-full'>
            <TableOfContents
              course={courseData}
              onNavigate={onNavigateToSection}
              expandedModules={expandedModules}
              expandedChapters={expandedChapters}
              onToggleModule={onToggleModule}
              onToggleChapter={onToggleChapter}
            />
          </div>
        </div>

        {/* Main Content */}
        <div className='space-y-4 lg:col-span-3 min-w-0 overflow-y-auto'>
          {showCourseStructure && (
            <div className='rounded-lg border border-blue-200 bg-blue-50 p-4 relative'>
              <button
                onClick={() => setShowCourseStructure(false)}
                className='absolute top-3 right-3 p-1 rounded-full hover:bg-blue-100 transition-colors'
                aria-label='Tutup struktur kursus'
              >
                <X className='h-4 w-4 text-blue-600' />
              </button>
              <h3 className='mb-2 font-semibold text-blue-900 pr-8'>Struktur Kursus</h3>
              <p className='mb-3 text-sm text-blue-800'>
                Selesaikan semua modul secara berurutan. Setiap bab harus diselesaikan
                sebelum mengakses kuis. Kuis modul akan terbuka setelah menyelesaikan semua
                kuis bab.
              </p>
              <div className='grid grid-cols-1 gap-4 text-sm md:grid-cols-3'>
                <div className='flex items-center space-x-2'>
                  <BookMarked className='h-4 w-4 text-blue-600' />
                  <span>Jalur Pembelajaran Berurutan</span>
                </div>
                <div className='flex items-center space-x-2'>
                  <Target className='h-4 w-4 text-blue-600' />
                  <span>Kuis Interaktif</span>
                </div>
                <div className='flex items-center space-x-2'>
                  <Award className='h-4 w-4 text-blue-600' />
                  <span>Sertifikasi Profesional</span>
                </div>
              </div>
            </div>
          )}

          {/* Module Expand/Collapse Controls */}

          {/* Course Modules */}
          <div className='space-y-6'>
            {courseData.modules.map((module) => (
              <div key={module.id} data-module-id={module.id}>
                <ModuleSection
                  module={module}
                  expandedContents={expandedContents}
                  expandedChapters={expandedChapters}
                  onToggleContent={onToggleContent}
                  onToggleContentComplete={onToggleContentComplete}
                  onStartQuiz={onStartQuiz}
                  isExpanded={expandedModules[module.id] || false}
                  onToggleExpanded={() => onToggleModule(module.id)}
                  onToggleChapter={onToggleChapter}
                  onExpandAllChapters={() =>
                    onExpandAllChaptersInModule(module.id)
                  }
                  onCollapseAllChapters={() =>
                    onCollapseAllChaptersInModule(module.id)
                  }
                />
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};
